# Clipboard Preview Modal Feature - Implementation Plan

## Overview
Add a preview modal that displays the exact content that will be copied to clipboard when users click "Copy All Selected". This allows users to review the combined content (files, prompts, instructions) before copying.

## Feature Requirements

### Functional Requirements
1. **Preview Button**: Add a "Preview" button next to the existing "Copy All Selected" button
2. **Preview Modal**: Display a modal showing the exact formatted content that would be copied
3. **Content Display**: Show the content with proper syntax highlighting where applicable
4. **Token Count**: Display total token count for the preview content
5. **Copy from Preview**: Allow copying directly from the preview modal
6. **Search/Find**: Enable text search within the preview (stretch goal)

### Non-Functional Requirements
- Reuse existing modal patterns and styles for consistency
- Handle large content efficiently (lazy rendering/virtualization if needed)
- Maintain responsive UI during content generation
- Preserve the exact formatting that would be copied

## Technical Architecture

### Component Structure
```
src/components/
├── clipboard-preview-modal.tsx  (NEW)
├── clipboard-preview-modal.css  (NEW) - Dedicated CSS module
└── content-area.tsx             (MODIFY)

src/styles/
└── index.css                    (NO CHANGES - styles stay in dedicated module)
```

### State Management
```typescript
// In use-modal-state.ts - Add new modal state
const [clipboardPreviewModalOpen, setClipboardPreviewModalOpen] = useState(false);
const [previewContent, setPreviewContent] = useState<string>("");
const [previewTokenCount, setPreviewTokenCount] = useState<number>(0);
```

## Implementation Details

### 1. Modal Component Structure (`clipboard-preview-modal.tsx`)

```typescript
// Import dedicated CSS module at top of file
import './clipboard-preview-modal.css';

interface ClipboardPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  content: string;
  tokenCount: number;
  onCopy: () => void;
}
```

Key features:
- Display content in a scrollable container
- Show token count in header
- Provide "Copy to Clipboard" button within modal
- Close button and ESC key support
- Optional: Syntax highlighting for code blocks
- Optional: Toggle between raw and formatted view

### 2. Content Display Strategy

#### Option A: Plain Text Display (Simple)
- Display content as plain text in a `<pre>` element
- Fast and handles large content well
- No syntax highlighting

#### Option B: Mixed Rendering (Recommended)
- Parse content to identify code blocks
- Render code blocks with syntax highlighting
- Render other content as plain text
- Better visual representation

#### Option C: Full Syntax Highlighting (Complex)
- Parse entire content structure
- Apply appropriate highlighting to each section
- Most visually appealing but potentially slower

### 3. Integration with Content Area

Modify `content-area.tsx`:
```typescript
// Add preview button next to Copy button
<button 
  className="preview-button"
  onClick={handlePreview}
>
  <Eye size={16} />
  <span>Preview</span>
</button>

// Handler function
const handlePreview = async () => {
  // Generate content (same as copy)
  const content = await handleCopyWithLoading(getSelectedFilesContent);
  setPreviewContent(content);
  setPreviewTokenCount(calculateTokens(content));
  setClipboardPreviewModalOpen(true);
};
```

### 4. Performance Considerations

#### For Large Content (>10MB):
1. **Virtualization**: Use react-window or similar for rendering only visible portions
2. **Chunked Rendering**: Render content in chunks with requestIdleCallback
3. **Loading State**: Show loading indicator while content generates
4. **Debounced Search**: If implementing search, debounce input

#### Token Counting:
- Reuse existing token counting utilities
- Consider caching token count if content hasn't changed
- Show estimation for very large content

### 5. UI/UX Design

#### Modal Layout:
```
┌────────────────────────────────────────────┐
│ Preview Clipboard Content      [X] Close   │
├────────────────────────────────────────────┤
│ Total: ~12,345 tokens                      │
├────────────────────────────────────────────┤
│ ┌──────────────────────────────────────┐   │
│ │                                      │   │
│ │     [Scrollable Content Area]        │   │
│ │                                      │   │
│ └──────────────────────────────────────┘   │
├────────────────────────────────────────────┤
│ [Close]              [Copy to Clipboard]   │
└────────────────────────────────────────────┘
```

#### Styling Approach:
- Create dedicated `clipboard-preview-modal.css` module
- Import styles only in `clipboard-preview-modal.tsx` component
- Follow existing CSS module patterns (like `file-view-modal.css`, `copy-button.css`)
- Reuse common modal patterns but keep styles isolated
- Dark/light theme support using existing theme context
- Monospace font for code sections
- Max height with scroll for content area
- NO styles added to `index.css` - maintain component encapsulation

## Implementation Steps

### Phase 1: Basic Preview (MVP)
1. ✅ Add modal state to `use-modal-state.ts`
2. ✅ Create `clipboard-preview-modal.tsx` component
3. ✅ Create `clipboard-preview-modal.css` with dedicated styles
4. ✅ Import CSS module in component: `import './clipboard-preview-modal.css'`
5. ✅ Display plain text content in scrollable area
6. ✅ Add token count display
7. ✅ Integrate preview button in `content-area.tsx`
8. ✅ Add copy functionality within modal

### Phase 2: Enhanced Display
1. ⬜ Parse content to identify code blocks
2. ⬜ Add syntax highlighting for code blocks
3. ⬜ Improve visual separation between sections
4. ⬜ Add line numbers option

### Phase 3: Advanced Features
1. ⬜ Add search/find functionality
2. ⬜ Implement content virtualization for performance
3. ⬜ Add toggle for raw/formatted view
4. ⬜ Export to file option

## Code Reuse Opportunities

### From FileViewModal:
- Modal structure patterns (but not styles - keep CSS isolated)
- Syntax highlighting setup (Prism)
- Theme integration approach
- Token counting display logic
- Scrollable content container patterns

### From Other Components:
- CopyButton component for copy functionality
- Content formatting from `content-formatter.ts`
- Token counting from `token-utils.ts`

### CSS Module Pattern Examples:
- `file-view-modal.css` - Reference for modal styling patterns
- `copy-button.css` - Reference for button styling
- `instruction-card.css` - Reference for component-specific CSS modules
- Each component maintains its own CSS file, imported only in that component

## Testing Considerations

### Unit Tests:
- Modal opens/closes correctly
- Content matches what would be copied
- Token count is accurate
- Copy functionality works
- Keyboard shortcuts (ESC to close)

### Integration Tests:
- Preview updates when selection changes
- Works with all content types (files, prompts, instructions)
- Handles empty selections gracefully
- Theme switching works correctly

### Performance Tests:
- Large content handling (>10MB)
- Many files selected (>100 files)
- Complex formatting scenarios

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management (trap focus in modal)
- High contrast mode support

## Migration & Backwards Compatibility

- No breaking changes to existing functionality
- Preview is optional - existing copy flow unchanged
- No data migration required
- Settings could be added for default behavior

## Future Enhancements

1. **Preview History**: Keep recent previews for quick access
2. **Format Options**: Different output formats (Markdown, Plain, XML)
3. **Template System**: Save preview as template for reuse
4. **Diff View**: Show changes from last preview
5. **Share Feature**: Generate shareable link for preview
6. **AI Token Estimation**: More accurate token counting for different models

## Risk Mitigation

### Performance Risks:
- **Risk**: Modal slow to open with large content
- **Mitigation**: Show loading state, generate content async

### Memory Risks:
- **Risk**: Large content causes memory issues
- **Mitigation**: Implement content streaming or pagination

### UX Risks:
- **Risk**: Users confused by preview vs actual copy
- **Mitigation**: Clear labeling, consistent behavior

## Success Metrics

- Users utilize preview before copying (>50% adoption)
- Reduced copy-paste errors reported
- No performance degradation
- Positive user feedback on clarity

## Timeline Estimate

- Phase 1 (MVP): 2-3 hours
- Phase 2 (Enhanced): 2-3 hours  
- Phase 3 (Advanced): 4-6 hours
- Testing & Polish: 2-3 hours

**Total: 10-15 hours for full implementation**

## Decision Points

1. **Syntax Highlighting**: Full highlighting vs plain text?
   - Recommendation: Start with plain text, add highlighting in Phase 2

2. **Content Generation**: Generate on button click vs pre-generate?
   - Recommendation: Generate on click to avoid unnecessary computation

3. **Modal Size**: Full screen vs large modal?
   - Recommendation: Large modal (80% viewport) with option to maximize

4. **Copy Behavior**: Copy from preview closes modal?
   - Recommendation: Keep modal open, show success toast

## Conclusion

This feature will significantly improve the user experience by allowing them to preview exactly what will be copied before committing to the clipboard. The phased approach ensures we can deliver value quickly while iterating on enhancements based on user feedback.